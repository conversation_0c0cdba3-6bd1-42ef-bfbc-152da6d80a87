<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionConst;
use App\Http\Consts\MissionPersonConst;
use App\Models\ImportItem;
use App\Models\MissionPerson;
use App\Models\MissionPhysicalExamination;
use Illuminate\Support\Str;

class MissionPhysicalSpotCheckResultCheckService
{
    use ErrorTrait;

    private $task;

    private $missionId;

    public function handle($task)
    {
        $this->task      = $task;
        $this->missionId = $task->params['mission_id'] ?? null;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE');

        $queryBuilder = ImportItem::query()->where('task_id', $task->id)->whereIn('checked', [
            ImportConst::importItemCheckUncheck,
            ImportConst::importItemCheckError,
        ]);

        $queryBuilder->clone()->where('type', ImportConst::importItemTypeMissionPhysicalSpotCheckResult)->chunkById($INSERT_BATCH_SIZE, function ($items) use ($task) {
            $this->checkPhysicalSpotCheckResult($items);
        });
    }

    public function checkPhysicalSpotCheckResult($items)
    {
        foreach ($items as $item) {
            $message = '';
            $data    = $item['data'];
            $name    = $data['name'];
            $idCard  = $data['id_card'];
            $result  = $data['result'];
            $remark  = $data['remark'] ?? '';

            if (!$name) {
                $message = "姓名不能为空";
            }
            if (!$idCard) {
                $message = "身份证不能为空";
            }
            if (Str::length($idCard) != 18) {
                $message = "身份证长度应为18位";
            }
            if (!$result) {
                $message = "抽查结果不能为空";
            } else {
                // 验证结果值是否有效
                $validResults = ['正常', '异常', '1', '2'];
                if (!in_array($result, $validResults)) {
                    $message = "抽查结果值无效，应为：正常、异常、1或2";
                } else {
                    // 如果结果是异常，检查是否填写了异常原因
                    if (in_array($result, ['异常', '2']) && empty($remark)) {
                        $message = "抽查结果为异常时，异常原因不能为空";
                    }
                }
            }

            // 检查人员是否存在
            if (!$message && $this->missionId) {
                $person = MissionPerson::query()
                    ->where('mission_id', $this->missionId)
                    ->where('id_card', $idCard)
                    ->first();

                if (!$person) {
                    $message = "未找到对应的人员记录";
                } else {
                    // 检查人员是否在已提交的计划批次里面
                    if ($person->mission_physical_examination_id) {
                        $physicalExamination = MissionPhysicalExamination::query()
                            ->where('id', $person->mission_physical_examination_id)
                            ->first();

                        if (!$physicalExamination || $physicalExamination->status != MissionConst::missionPhysicalExaminationStatusSubmitted) {
                            $message = "该人员不在已提交的计划批次中";
                        }
                    } else {
                        $message = "该人员未分配到任何计划批次";
                    }
                }
            }

            if ($message) {
                $item->update([
                    "message" => $message,
                    "checked" => ImportConst::importItemCheckError,
                ]);
            } else {
                $item->update([
                    "checked" => ImportConst::importItemCheckCheckAccepted,
                ]);
            }
        }
    }
}
